"""
Core models for HTEZ School Management System.

This module contains shared models used across the application:
- SchoolYear: Academic year management
- Semester: Academic semester management  
- Subject: Academic subjects
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from model_utils.models import TimeStampedModel


class SchoolYear(TimeStampedModel):
    """学年模型 - 作为学期的容器，用于数据隔离"""
    
    name = models.CharField(
        _("学年名称"), 
        max_length=20, 
        unique=True,
        help_text=_("例如：2024-2025学年")
    )
    start_year = models.PositiveIntegerField(
        _("开始年份"),
        validators=[MinValueValidator(2020), MaxValueValidator(2050)]
    )
    end_year = models.PositiveIntegerField(
        _("结束年份"),
        validators=[MinValueValidator(2020), MaxValueValidator(2050)]
    )
    is_current = models.BooleanField(
        _("当前学年"), 
        default=False,
        help_text=_("标记当前活跃的学年")
    )
    
    class Meta:
        verbose_name = _("学年")
        verbose_name_plural = _("学年")
        ordering = ["-start_year"]
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # 确保只有一个当前学年
        if self.is_current:
            SchoolYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Semester(TimeStampedModel):
    """学期模型 - 所有教学活动的核心时间单位"""
    
    SEMESTER_CHOICES = [
        (1, _("第一学期")),
        (2, _("第二学期")),
    ]
    
    school_year = models.ForeignKey(
        SchoolYear,
        on_delete=models.CASCADE,
        related_name="semesters",
        verbose_name=_("学年")
    )
    semester_number = models.PositiveSmallIntegerField(
        _("学期序号"),
        choices=SEMESTER_CHOICES
    )
    name = models.CharField(
        _("学期名称"),
        max_length=30,
        help_text=_("例如：2024-2025学年第一学期")
    )
    start_date = models.DateField(_("开始日期"))
    end_date = models.DateField(_("结束日期"))
    is_current = models.BooleanField(
        _("当前学期"), 
        default=False,
        help_text=_("标记当前活跃的学期")
    )
    
    class Meta:
        verbose_name = _("学期")
        verbose_name_plural = _("学期")
        unique_together = ["school_year", "semester_number"]
        ordering = ["-school_year__start_year", "-semester_number"]
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # 自动生成学期名称
        if not self.name:
            self.name = f"{self.school_year.name}第{self.semester_number}学期"
        
        # 确保只有一个当前学期
        if self.is_current:
            Semester.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class Subject(TimeStampedModel):
    """学科模型"""
    
    SUBJECT_CATEGORIES = [
        ("main", _("主要学科")),
        ("minor", _("副科")),
        ("art", _("艺术类")),
        ("pe", _("体育类")),
        ("tech", _("技术类")),
        ("other", _("其他")),
    ]
    
    name = models.CharField(
        _("学科名称"), 
        max_length=20, 
        unique=True
    )
    code = models.CharField(
        _("学科代码"), 
        max_length=10, 
        unique=True,
        help_text=_("用于系统内部标识，如：MATH, CHINESE")
    )
    category = models.CharField(
        _("学科类别"),
        max_length=10,
        choices=SUBJECT_CATEGORIES,
        default="main"
    )
    is_active = models.BooleanField(
        _("是否启用"), 
        default=True
    )
    sort_order = models.PositiveIntegerField(
        _("排序"),
        default=0,
        help_text=_("数字越小排序越靠前")
    )
    
    class Meta:
        verbose_name = _("学科")
        verbose_name_plural = _("学科")
        ordering = ["sort_order", "name"]
    
    def __str__(self):
        return self.name
