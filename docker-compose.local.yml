volumes:
  htez_local_postgres_data: {}
  htez_local_postgres_data_backups: {}
  htez_local_redis_data: {}

services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: htez_local_django
    container_name: htez_local_django
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app:z
    env_file:
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    ports:
      - '8000:8000'
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: htez_production_postgres
    container_name: htez_local_postgres
    volumes:
      - htez_local_postgres_data:/var/lib/postgresql/data
      - htez_local_postgres_data_backups:/backups
    env_file:
      - ./.envs/.local/.postgres

  redis:
    image: docker.io/redis:6
    container_name: htez_local_redis
    volumes:
      - htez_local_redis_data:/data

  celeryworker:
    <<: *django
    image: htez_local_celeryworker
    container_name: htez_local_celeryworker
    depends_on:
      - redis
      - postgres
    ports: []
    command: /start-celeryworker

  celerybeat:
    <<: *django
    image: htez_local_celerybeat
    container_name: htez_local_celerybeat
    depends_on:
      - redis
      - postgres
    ports: []
    command: /start-celerybeat

  flower:
    <<: *django
    image: htez_local_flower
    container_name: htez_local_flower
    ports:
      - '5555:5555'
    command: /start-flower
