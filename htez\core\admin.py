from django.contrib import admin
from .models import SchoolYear, Semester, Subject


@admin.register(SchoolYear)
class SchoolYearAdmin(admin.ModelAdmin):
    list_display = ['name', 'start_year', 'end_year', 'is_current', 'created']
    list_filter = ['is_current', 'start_year']
    search_fields = ['name']
    ordering = ['-start_year']

    def save_model(self, request, obj, form, change):
        # 确保只有一个当前学年
        if obj.is_current:
            SchoolYear.objects.filter(is_current=True).update(is_current=False)
        super().save_model(request, obj, form, change)


@admin.register(Semester)
class SemesterAdmin(admin.ModelAdmin):
    list_display = ['name', 'school_year', 'semester_number', 'start_date', 'end_date', 'is_current']
    list_filter = ['is_current', 'semester_number', 'school_year']
    search_fields = ['name', 'school_year__name']
    ordering = ['-school_year__start_year', '-semester_number']

    def save_model(self, request, obj, form, change):
        # 确保只有一个当前学期
        if obj.is_current:
            Semester.objects.filter(is_current=True).update(is_current=False)
        super().save_model(request, obj, form, change)


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'category', 'is_active', 'sort_order']
    list_filter = ['category', 'is_active']
    search_fields = ['name', 'code']
    ordering = ['sort_order', 'name']
    list_editable = ['sort_order', 'is_active']
