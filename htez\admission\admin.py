from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from .models import AdmissionRecord


class AdmissionRecordResource(resources.ModelResource):
    """录取记录导入导出资源"""

    class Meta:
        model = AdmissionRecord
        fields = ('name', 'id_card', 'gender', 'phone', 'score', 'admission_type', 'remarks', 'admission_year')
        export_order = fields
        import_id_fields = ('id_card',)
        skip_unchanged = True
        report_skipped = True

    def before_import_row(self, row, **kwargs):
        """导入前处理数据"""
        # 确保性别字段正确映射
        if 'gender' in row:
            if row['gender'] in ['男', 'M', 'Male']:
                row['gender'] = 'M'
            elif row['gender'] in ['女', 'F', 'Female']:
                row['gender'] = 'F'

        # 确保录取类型正确映射
        if 'admission_type' in row:
            type_mapping = {
                '指标分配': 'quota',
                '剩余计划': 'remaining',
                '特长生': 'special'
            }
            if row['admission_type'] in type_mapping:
                row['admission_type'] = type_mapping[row['admission_type']]


@admin.register(AdmissionRecord)
class AdmissionRecordAdmin(ImportExportModelAdmin):
    resource_class = AdmissionRecordResource

    list_display = [
        'name', 'masked_id_card', 'gender', 'score',
        'admission_type', 'admission_year', 'is_queried',
        'query_count', 'created'
    ]
    list_filter = [
        'admission_year', 'admission_type', 'gender',
        'is_queried', 'created'
    ]
    search_fields = ['name', 'id_card']
    ordering = ['-admission_year', '-score']
    readonly_fields = ['query_count', 'last_query_time', 'is_queried']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'id_card', 'gender', 'phone')
        }),
        ('录取信息', {
            'fields': ('score', 'admission_type', 'admission_year', 'remarks')
        }),
        ('查询统计', {
            'fields': ('is_queried', 'query_count', 'last_query_time'),
            'classes': ('collapse',)
        }),
    )

    def masked_id_card(self, obj):
        """显示脱敏的身份证号"""
        return obj.masked_id_card
    masked_id_card.short_description = '身份证号'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related()
