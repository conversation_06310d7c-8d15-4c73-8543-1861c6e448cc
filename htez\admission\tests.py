"""
Tests for admission models.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import AdmissionRecord


class AdmissionRecordModelTest(TestCase):
    """Test AdmissionRecord model"""
    
    def test_create_admission_record(self):
        """Test creating an admission record"""
        record = AdmissionRecord.objects.create(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        self.assertEqual(str(record), "张三 - 指标分配")
        self.assertFalse(record.is_queried)
        self.assertEqual(record.query_count, 0)
    
    def test_id_card_validation(self):
        """Test ID card validation"""
        # Valid ID card
        record = AdmissionRecord(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        record.full_clean()  # Should not raise exception
        
        # Invalid ID card (too short)
        record.id_card = "12345"
        with self.assertRaises(ValidationError):
            record.full_clean()
    
    def test_phone_validation(self):
        """Test phone number validation"""
        record = AdmissionRecord(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        record.full_clean()  # Should not raise exception
        
        # Invalid phone number
        record.phone = "123456"
        with self.assertRaises(ValidationError):
            record.full_clean()
    
    def test_masked_id_card_property(self):
        """Test masked ID card property"""
        record = AdmissionRecord.objects.create(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        self.assertEqual(record.masked_id_card, "370102********1234")
    
    def test_id_card_last_four_property(self):
        """Test ID card last four digits property"""
        record = AdmissionRecord.objects.create(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        self.assertEqual(record.id_card_last_four, "1234")
    
    def test_search_by_name_and_id_suffix(self):
        """Test search by name and ID suffix"""
        record = AdmissionRecord.objects.create(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        
        # Should find the record
        found = AdmissionRecord.search_by_name_and_id_suffix("张三", "1234")
        self.assertEqual(found, record)
        
        # Should not find with wrong name
        not_found = AdmissionRecord.search_by_name_and_id_suffix("李四", "1234")
        self.assertIsNone(not_found)
        
        # Should not find with wrong ID suffix
        not_found = AdmissionRecord.search_by_name_and_id_suffix("张三", "5678")
        self.assertIsNone(not_found)
    
    def test_increment_query_count(self):
        """Test incrementing query count"""
        record = AdmissionRecord.objects.create(
            name="张三",
            id_card="370102200501011234",
            gender="M",
            phone="13812345678",
            score=650.5,
            admission_type="quota",
            admission_year=2024
        )
        
        self.assertEqual(record.query_count, 0)
        self.assertFalse(record.is_queried)
        
        record.increment_query_count()
        
        self.assertEqual(record.query_count, 1)
        self.assertTrue(record.is_queried)
        self.assertIsNotNone(record.last_query_time)
