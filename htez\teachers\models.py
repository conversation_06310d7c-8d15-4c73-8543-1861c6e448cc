"""
Teachers models for HTEZ School Management System.

This module contains models for managing teachers and classes:
- GradeLevel: Grade level management (高一、高二、高三)
- Teacher: Teacher information
- SchoolClass: Class management
- TeachingAssignment: Teaching assignments
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from model_utils.models import TimeStampedModel


class GradeLevel(TimeStampedModel):
    """级部模型"""
    
    GRADE_CHOICES = [
        (1, _("高一级部")),
        (2, _("高二级部")),
        (3, _("高三级部")),
    ]
    
    name = models.CharField(
        _("级部名称"),
        max_length=20,
        unique=True
    )
    grade_number = models.PositiveSmallIntegerField(
        _("年级"),
        choices=GRADE_CHOICES,
        unique=True
    )
    is_active = models.BooleanField(
        _("是否启用"),
        default=True
    )
    
    class Meta:
        verbose_name = _("级部")
        verbose_name_plural = _("级部")
        ordering = ["grade_number"]
    
    def __str__(self):
        return self.name


class Teacher(TimeStampedModel):
    """教师信息模型"""
    
    # 手机号验证器
    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message=_("请输入有效的11位手机号码")
    )
    
    # 基本信息
    name = models.CharField(
        _("姓名"),
        max_length=50
    )
    phone = models.CharField(
        _("联系电话"),
        max_length=11,
        validators=[phone_validator],
        unique=True
    )
    
    # 教学信息
    subjects = models.ManyToManyField(
        "core.Subject",
        verbose_name=_("所教学科"),
        related_name="teachers",
        help_text=_("教师可以教授多个学科")
    )
    
    # 当前任教信息
    current_grade_level = models.ForeignKey(
        GradeLevel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("当前任教级部"),
        related_name="teachers"
    )
    
    # 职务信息
    is_grade_subject_leader = models.BooleanField(
        _("级部学科组长"),
        default=False,
        help_text=_("是否为本学年级部学科组长")
    )
    is_school_subject_leader = models.BooleanField(
        _("学校学科组长"),
        default=False,
        help_text=_("是否为本学年学校学科组长")
    )
    
    # 状态
    is_active = models.BooleanField(
        _("在职状态"),
        default=True
    )
    
    class Meta:
        verbose_name = _("教师")
        verbose_name_plural = _("教师")
        ordering = ["name"]
    
    def __str__(self):
        return self.name
    
    @property
    def subject_names(self):
        """获取所教学科名称列表"""
        return [subject.name for subject in self.subjects.all()]
    
    @property
    def is_class_teacher(self):
        """是否为班主任（当前学期）"""
        from htez.core.models import Semester
        current_semester = Semester.objects.filter(is_current=True).first()
        if not current_semester:
            return False
        
        return self.teaching_classes.filter(
            semester=current_semester,
            is_class_teacher=True
        ).exists()


class SchoolClass(TimeStampedModel):
    """班级模型 - 与学期关联，代表特定学期的班级实体"""
    
    semester = models.ForeignKey(
        "core.Semester",
        on_delete=models.CASCADE,
        verbose_name=_("学期"),
        related_name="classes"
    )
    grade_level = models.ForeignKey(
        GradeLevel,
        on_delete=models.CASCADE,
        verbose_name=_("级部"),
        related_name="classes"
    )
    
    name = models.CharField(
        _("班级名称"),
        max_length=20,
        help_text=_("例如：高一1班")
    )
    class_number = models.PositiveSmallIntegerField(
        _("班级序号"),
        help_text=_("在同级部中的班级序号")
    )
    
    # 班级容量
    max_students = models.PositiveIntegerField(
        _("最大学生数"),
        default=50
    )
    
    class Meta:
        verbose_name = _("班级")
        verbose_name_plural = _("班级")
        unique_together = ["semester", "grade_level", "class_number"]
        ordering = ["semester", "grade_level", "class_number"]
    
    def __str__(self):
        return f"{self.semester.school_year.name} {self.name}"
    
    @property
    def current_student_count(self):
        """当前学生数量"""
        return self.students.count()
    
    @property
    def class_teacher(self):
        """班主任"""
        assignment = self.teaching_assignments.filter(is_class_teacher=True).first()
        return assignment.teacher if assignment else None


class TeachingAssignment(TimeStampedModel):
    """任课安排模型 - 清晰描述教师、班级、学科在特定学期的关系"""
    
    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        verbose_name=_("教师"),
        related_name="teaching_assignments"
    )
    school_class = models.ForeignKey(
        SchoolClass,
        on_delete=models.CASCADE,
        verbose_name=_("班级"),
        related_name="teaching_assignments"
    )
    subject = models.ForeignKey(
        "core.Subject",
        on_delete=models.CASCADE,
        verbose_name=_("学科"),
        related_name="teaching_assignments"
    )
    
    # 职务信息
    is_class_teacher = models.BooleanField(
        _("是否班主任"),
        default=False
    )
    
    class Meta:
        verbose_name = _("任课安排")
        verbose_name_plural = _("任课安排")
        unique_together = ["teacher", "school_class", "subject"]
        ordering = ["school_class", "subject", "teacher"]
    
    def __str__(self):
        role = _("班主任") if self.is_class_teacher else _("任课教师")
        return f"{self.teacher.name} - {self.school_class.name} - {self.subject.name} ({role})"
