"""
Admission models for HTEZ School Management System.

This module contains models for managing student admission:
- AdmissionRecord: New student admission records
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from model_utils.models import TimeStampedModel


class AdmissionRecord(TimeStampedModel):
    """新生录取记录模型"""
    
    ADMISSION_TYPE_CHOICES = [
        ("quota", _("指标分配")),
        ("remaining", _("剩余计划")),
        ("special", _("特长生")),
    ]
    
    GENDER_CHOICES = [
        ("M", _("男")),
        ("F", _("女")),
    ]
    
    # 身份证号验证器
    id_card_validator = RegexValidator(
        regex=r'^\d{17}[\dXx]$',
        message=_("请输入有效的18位身份证号码")
    )
    
    # 手机号验证器
    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message=_("请输入有效的11位手机号码")
    )
    
    # 基本信息
    name = models.CharField(
        _("姓名"), 
        max_length=50
    )
    id_card = models.CharField(
        _("身份证号"),
        max_length=18,
        validators=[id_card_validator],
        unique=True,
        help_text=_("18位二代身份证号码")
    )
    gender = models.CharField(
        _("性别"),
        max_length=1,
        choices=GENDER_CHOICES
    )
    phone = models.CharField(
        _("联系电话"),
        max_length=11,
        validators=[phone_validator],
        help_text=_("11位手机号码")
    )
    
    # 录取信息
    score = models.FloatField(
        _("中考成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(1000)],
        help_text=_("中考总成绩")
    )
    admission_type = models.CharField(
        _("录取类型"),
        max_length=20,
        choices=ADMISSION_TYPE_CHOICES
    )
    remarks = models.TextField(
        _("备注"),
        blank=True,
        help_text=_("其他备注信息")
    )
    
    # 录取年份
    admission_year = models.PositiveIntegerField(
        _("录取年份"),
        validators=[MinValueValidator(2020), MaxValueValidator(2050)],
        help_text=_("录取的年份")
    )
    
    # 查询状态
    is_queried = models.BooleanField(
        _("已查询"),
        default=False,
        help_text=_("学生是否已查询过录取信息")
    )
    query_count = models.PositiveIntegerField(
        _("查询次数"),
        default=0,
        help_text=_("学生查询录取信息的次数")
    )
    last_query_time = models.DateTimeField(
        _("最后查询时间"),
        null=True,
        blank=True
    )
    
    class Meta:
        verbose_name = _("录取记录")
        verbose_name_plural = _("录取记录")
        ordering = ["-admission_year", "score"]
        indexes = [
            models.Index(fields=["name", "id_card"]),
            models.Index(fields=["admission_year"]),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.get_admission_type_display()}"
    
    @property
    def id_card_last_four(self):
        """获取身份证号后四位"""
        return self.id_card[-4:] if self.id_card else ""
    
    @property
    def masked_id_card(self):
        """获取脱敏的身份证号（中间用*号替换）"""
        if not self.id_card or len(self.id_card) != 18:
            return self.id_card
        return f"{self.id_card[:6]}********{self.id_card[-4:]}"
    
    def increment_query_count(self):
        """增加查询次数"""
        from django.utils import timezone
        self.query_count += 1
        self.last_query_time = timezone.now()
        if not self.is_queried:
            self.is_queried = True
        self.save(update_fields=['query_count', 'last_query_time', 'is_queried'])
    
    @classmethod
    def search_by_name_and_id_suffix(cls, name, id_suffix, admission_year=None):
        """
        根据姓名和身份证后四位查询录取记录
        
        Args:
            name: 学生姓名
            id_suffix: 身份证后四位
            admission_year: 录取年份（可选）
        
        Returns:
            AdmissionRecord instance or None
        """
        queryset = cls.objects.filter(
            name=name,
            id_card__endswith=id_suffix
        )
        
        if admission_year:
            queryset = queryset.filter(admission_year=admission_year)
        
        return queryset.first()
