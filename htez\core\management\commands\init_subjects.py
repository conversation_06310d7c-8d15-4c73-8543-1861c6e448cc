"""
Management command to initialize subjects data.
"""

from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from htez.core.models import Subject


class Command(BaseCommand):
    help = 'Initialize subjects data for HTEZ School Management System'
    
    def handle(self, *args, **options):
        """Initialize subjects data"""
        
        subjects_data = [
            # 主要学科
            {'name': '语文', 'code': 'CHINESE', 'category': 'main', 'sort_order': 1},
            {'name': '数学', 'code': 'MATH', 'category': 'main', 'sort_order': 2},
            {'name': '英语', 'code': 'ENGLISH', 'category': 'main', 'sort_order': 3},
            {'name': '俄语', 'code': 'RUSSIAN', 'category': 'main', 'sort_order': 4},
            {'name': '西班牙语', 'code': 'SPANISH', 'category': 'main', 'sort_order': 5},
            {'name': '日语', 'code': 'JAPANESE', 'category': 'main', 'sort_order': 6},
            {'name': '物理', 'code': 'PHYSICS', 'category': 'main', 'sort_order': 7},
            {'name': '化学', 'code': 'CHEMISTRY', 'category': 'main', 'sort_order': 8},
            {'name': '生物', 'code': 'BIOLOGY', 'category': 'main', 'sort_order': 9},
            {'name': '地理', 'code': 'GEOGRAPHY', 'category': 'main', 'sort_order': 10},
            {'name': '历史', 'code': 'HISTORY', 'category': 'main', 'sort_order': 11},
            
            # 艺术类
            {'name': '美术', 'code': 'ART', 'category': 'art', 'sort_order': 20},
            {'name': '音乐', 'code': 'MUSIC', 'category': 'art', 'sort_order': 21},
            {'name': '书法', 'code': 'CALLIGRAPHY', 'category': 'art', 'sort_order': 22},
            
            # 体育类
            {'name': '体育', 'code': 'PE', 'category': 'pe', 'sort_order': 30},
            
            # 技术类
            {'name': '信息技术', 'code': 'IT', 'category': 'tech', 'sort_order': 40},
            {'name': '通用技术', 'code': 'GENERAL_TECH', 'category': 'tech', 'sort_order': 41},
            
            # 其他
            {'name': '职业发展', 'code': 'CAREER', 'category': 'other', 'sort_order': 50},
            {'name': '心理健康', 'code': 'PSYCHOLOGY', 'category': 'other', 'sort_order': 51},
        ]
        
        created_count = 0
        updated_count = 0
        
        for subject_data in subjects_data:
            subject, created = Subject.objects.get_or_create(
                code=subject_data['code'],
                defaults=subject_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created subject: {subject.name}')
                )
            else:
                # Update existing subject
                for key, value in subject_data.items():
                    if key != 'code':  # Don't update the code
                        setattr(subject, key, value)
                subject.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated subject: {subject.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully initialized subjects: '
                f'{created_count} created, {updated_count} updated'
            )
        )
