"""
Students models for HTEZ School Management System.

This module contains models for managing student information:
- Student: Student basic information
- Guardian: Guardian information
- StudentEnrollment: Student enrollment records per semester
- FamilyInfo: Family background information
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from model_utils.models import TimeStampedModel


class Student(TimeStampedModel):
    """学生详细信息模型"""
    
    GENDER_CHOICES = [
        ("M", _("男")),
        ("F", _("女")),
    ]
    
    POLITICAL_STATUS_CHOICES = [
        ("member", _("团员")),
        ("masses", _("群众")),
    ]
    
    GRADE_CHOICES = [
        ("A", "A"),
        ("B", "B"), 
        ("C", "C"),
        ("D", "D"),
        ("E", "E"),
    ]
    
    # 验证器
    id_card_validator = RegexValidator(
        regex=r'^\d{17}[\dXx]$',
        message=_("请输入有效的18位身份证号码")
    )
    
    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message=_("请输入有效的11位手机号码")
    )
    
    # 基本信息
    name = models.CharField(_("姓名"), max_length=50)
    gender = models.CharField(_("性别"), max_length=1, choices=GENDER_CHOICES)
    id_card = models.CharField(
        _("身份证号"),
        max_length=18,
        validators=[id_card_validator],
        unique=True
    )
    phone = models.CharField(
        _("手机号码"),
        max_length=11,
        validators=[phone_validator],
        blank=True,
        help_text=_("学生本人手机号（可选）")
    )
    political_status = models.CharField(
        _("政治面貌"),
        max_length=10,
        choices=POLITICAL_STATUS_CHOICES,
        default="masses"
    )
    
    # 地址信息
    home_address = models.TextField(
        _("现家庭住址"),
        help_text=_("需精确到门牌号")
    )
    
    # 初中信息
    junior_school_name = models.CharField(
        _("初中毕业学校名称"),
        max_length=100
    )
    junior_school_id = models.CharField(
        _("初中学籍号"),
        max_length=20,
        blank=True
    )
    exam_number = models.CharField(
        _("文化课考试号码"),
        max_length=10,
        help_text=_("十位文化课考试号码")
    )
    
    # 中考成绩
    total_score = models.FloatField(
        _("中考总分"),
        validators=[MinValueValidator(0), MaxValueValidator(1000)]
    )
    chinese_score = models.FloatField(
        _("语文成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(200)]
    )
    math_score = models.FloatField(
        _("数学成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(200)]
    )
    english_score = models.FloatField(
        _("英语成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(200)]
    )
    physics_score = models.FloatField(
        _("物理成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(200)]
    )
    chemistry_score = models.FloatField(
        _("化学成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(200)]
    )
    pe_score = models.FloatField(
        _("体育成绩"),
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    
    # 等级成绩
    moral_grade = models.CharField(_("道德与法制"), max_length=1, choices=GRADE_CHOICES)
    history_grade = models.CharField(_("历史"), max_length=1, choices=GRADE_CHOICES)
    geography_grade = models.CharField(_("地理"), max_length=1, choices=GRADE_CHOICES)
    biology_grade = models.CharField(_("生物"), max_length=1, choices=GRADE_CHOICES)
    comprehensive_grade = models.CharField(_("综合素质"), max_length=1, choices=GRADE_CHOICES)
    art_grade = models.CharField(_("艺术"), max_length=1, choices=GRADE_CHOICES)
    
    district_rank = models.PositiveIntegerField(
        _("区内位次"),
        help_text=_("在区内的排名位次")
    )
    
    # 入学年份
    admission_year = models.PositiveIntegerField(
        _("入学年份"),
        validators=[MinValueValidator(2020), MaxValueValidator(2050)]
    )
    
    # 报到信息
    wants_boarding = models.BooleanField(
        _("是否选择住校"),
        default=False
    )
    wants_uniform = models.BooleanField(
        _("是否自愿征订校服"),
        default=False
    )
    height = models.FloatField(
        _("身高(cm)"),
        null=True,
        blank=True,
        validators=[MinValueValidator(100), MaxValueValidator(250)]
    )
    weight = models.FloatField(
        _("体重(kg)"),
        null=True,
        blank=True,
        validators=[MinValueValidator(30), MaxValueValidator(200)]
    )
    uniform_size_large = models.BooleanField(
        _("校服加大"),
        default=False
    )
    uniform_size_fat = models.BooleanField(
        _("校服加肥"),
        default=False
    )
    
    class Meta:
        verbose_name = _("学生")
        verbose_name_plural = _("学生")
        ordering = ["admission_year", "name"]
        indexes = [
            models.Index(fields=["name", "id_card"]),
            models.Index(fields=["admission_year"]),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.admission_year}级)"


class Guardian(TimeStampedModel):
    """监护人信息模型"""

    RELATIONSHIP_CHOICES = [
        # 直系亲属关系
        ("father", _("父亲")),
        ("mother", _("母亲")),
        ("grandfather", _("祖父")),
        ("grandmother", _("祖母")),
        ("maternal_grandfather", _("外祖父")),
        ("maternal_grandmother", _("外祖母")),
        ("elder_brother", _("兄")),
        ("elder_sister", _("姐")),

        # 旁系亲属关系
        ("uncle_father_side", _("伯父")),
        ("aunt_father_side", _("伯母")),
        ("uncle_father_younger", _("叔父")),
        ("aunt_father_younger", _("婶婶")),
        ("uncle_father_sister", _("姑父")),
        ("aunt_father_sister", _("姑母")),
        ("uncle_mother_side", _("舅舅")),
        ("aunt_mother_side", _("舅妈")),
        ("uncle_mother_sister", _("姨父")),
        ("aunt_mother_sister", _("姨母")),

        # 特殊监护关系
        ("adoptive_father", _("养父")),
        ("adoptive_mother", _("养母")),
        ("step_father", _("继父")),
        ("step_mother", _("继母")),
        ("other_guardian", _("其他监护人")),
        ("social_institution", _("社会机构")),
    ]

    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message=_("请输入有效的11位手机号码")
    )

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name="guardians",
        verbose_name=_("学生")
    )
    name = models.CharField(_("监护人姓名"), max_length=50)
    relationship = models.CharField(
        _("监护人关系"),
        max_length=30,
        choices=RELATIONSHIP_CHOICES
    )
    phone = models.CharField(
        _("监护人联系方式"),
        max_length=11,
        validators=[phone_validator]
    )
    is_primary = models.BooleanField(
        _("主要监护人"),
        default=False,
        help_text=_("是否为主要监护人")
    )

    class Meta:
        verbose_name = _("监护人")
        verbose_name_plural = _("监护人")
        ordering = ["-is_primary", "name"]

    def __str__(self):
        return f"{self.student.name}的{self.get_relationship_display()} - {self.name}"


class FamilyInfo(TimeStampedModel):
    """家庭信息模型"""

    SPECIAL_STUDENT_TYPES = [
        ("T1", _("家庭经济困难")),
        ("T2", _("孤儿")),
        ("T3", _("事实无人抚养儿童")),
        ("T4", _("重点困境儿童")),
        ("T5", _("留守儿童")),
        ("T6", _("残疾儿童")),
        ("T7", _("特殊监护困境儿童")),
        ("T8", _("其他困境儿童")),
        ("T9", _("有不良行为学生")),
        ("T10", _("心理健康问题学生")),
        ("T11", _("辍学（有辍学倾向）学生")),
        ("T12", _("其他特殊类型")),
    ]

    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message=_("请输入有效的11位手机号码")
    )

    student = models.OneToOneField(
        Student,
        on_delete=models.CASCADE,
        related_name="family_info",
        verbose_name=_("学生")
    )

    # 特殊学生类型（可多选）
    special_types = models.JSONField(
        _("特殊学生类型"),
        default=list,
        blank=True,
        help_text=_("可多选，存储特殊学生类型代码列表")
    )

    # 父母信息（可选填）
    father_name = models.CharField(
        _("父亲姓名"),
        max_length=50,
        blank=True
    )
    father_phone = models.CharField(
        _("父亲手机号码"),
        max_length=11,
        validators=[phone_validator],
        blank=True
    )
    mother_name = models.CharField(
        _("母亲姓名"),
        max_length=50,
        blank=True
    )
    mother_phone = models.CharField(
        _("母亲手机号码"),
        max_length=11,
        validators=[phone_validator],
        blank=True
    )

    class Meta:
        verbose_name = _("家庭信息")
        verbose_name_plural = _("家庭信息")

    def __str__(self):
        return f"{self.student.name}的家庭信息"

    @property
    def special_type_display(self):
        """获取特殊学生类型的显示名称"""
        if not self.special_types:
            return _("无")

        type_dict = dict(self.SPECIAL_STUDENT_TYPES)
        return ", ".join([type_dict.get(t, t) for t in self.special_types])

    def has_special_type(self, type_code):
        """检查是否包含特定的特殊学生类型"""
        return type_code in (self.special_types or [])


class StudentEnrollment(TimeStampedModel):
    """学生在读记录模型 - 记录学生在每个学期的班级信息"""

    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name="enrollments",
        verbose_name=_("学生")
    )
    semester = models.ForeignKey(
        "core.Semester",
        on_delete=models.CASCADE,
        related_name="student_enrollments",
        verbose_name=_("学期")
    )
    school_class = models.ForeignKey(
        "teachers.SchoolClass",
        on_delete=models.CASCADE,
        related_name="students",
        verbose_name=_("班级")
    )

    # 学号
    student_number = models.CharField(
        _("学号"),
        max_length=20,
        help_text=_("在该学期的学号")
    )

    # 状态
    is_active = models.BooleanField(
        _("在读状态"),
        default=True,
        help_text=_("是否在该学期正常在读")
    )

    class Meta:
        verbose_name = _("学生在读记录")
        verbose_name_plural = _("学生在读记录")
        unique_together = ["student", "semester"]
        ordering = ["-semester", "school_class", "student_number"]

    def __str__(self):
        return f"{self.student.name} - {self.semester.name} - {self.school_class.name}"
