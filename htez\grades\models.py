"""
Grades models for HTEZ School Management System.

This module contains models for managing exams and student scores:
- Exam: Exam information
- StudentScore: Individual student scores for each exam and subject
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from model_utils.models import TimeStampedModel


class Exam(TimeStampedModel):
    """考试模型 - 定义每一次考试的基本信息"""
    
    EXAM_TYPE_CHOICES = [
        ("monthly", _("月考")),
        ("midterm", _("期中考试")),
        ("final", _("期末考试")),
        ("mock", _("模拟考试")),
        ("entrance", _("入学考试")),
        ("other", _("其他考试")),
    ]
    
    name = models.CharField(
        _("考试名称"),
        max_length=100,
        help_text=_("例如：2024-2025学年第一学期期中考试")
    )
    exam_type = models.CharField(
        _("考试类型"),
        max_length=20,
        choices=EXAM_TYPE_CHOICES
    )
    semester = models.ForeignKey(
        "core.Semester",
        on_delete=models.CASCADE,
        related_name="exams",
        verbose_name=_("学期")
    )
    
    # 考试时间
    start_date = models.DateField(_("考试开始日期"))
    end_date = models.DateField(_("考试结束日期"))
    
    # 考试范围
    grade_levels = models.ManyToManyField(
        "teachers.GradeLevel",
        verbose_name=_("参与级部"),
        related_name="exams",
        help_text=_("参与此次考试的级部")
    )
    subjects = models.ManyToManyField(
        "core.Subject",
        verbose_name=_("考试科目"),
        related_name="exams",
        help_text=_("此次考试包含的科目")
    )
    
    # 状态
    is_published = models.BooleanField(
        _("成绩已发布"),
        default=False,
        help_text=_("成绩是否已经发布给学生查看")
    )
    is_active = models.BooleanField(
        _("考试状态"),
        default=True
    )
    
    # 备注
    description = models.TextField(
        _("考试说明"),
        blank=True,
        help_text=_("考试的详细说明或备注")
    )
    
    class Meta:
        verbose_name = _("考试")
        verbose_name_plural = _("考试")
        ordering = ["-start_date", "name"]
        indexes = [
            models.Index(fields=["semester", "exam_type"]),
            models.Index(fields=["start_date"]),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_exam_type_display()})"
    
    @property
    def total_students(self):
        """参与考试的学生总数"""
        return self.student_scores.values('student').distinct().count()
    
    @property
    def total_subjects(self):
        """考试科目数量"""
        return self.subjects.count()


class StudentScore(TimeStampedModel):
    """学生成绩模型 - 记录单个学生在单次考试中单科的成绩"""
    
    student = models.ForeignKey(
        "students.Student",
        on_delete=models.CASCADE,
        related_name="scores",
        verbose_name=_("学生")
    )
    exam = models.ForeignKey(
        Exam,
        on_delete=models.CASCADE,
        related_name="student_scores",
        verbose_name=_("考试")
    )
    subject = models.ForeignKey(
        "core.Subject",
        on_delete=models.CASCADE,
        related_name="student_scores",
        verbose_name=_("科目")
    )
    
    # 成绩信息
    score = models.FloatField(
        _("得分"),
        validators=[MinValueValidator(0), MaxValueValidator(200)],
        help_text=_("学生在该科目的得分")
    )
    full_score = models.FloatField(
        _("满分"),
        validators=[MinValueValidator(1), MaxValueValidator(200)],
        default=100,
        help_text=_("该科目的满分")
    )
    
    # 排名信息
    class_rank = models.PositiveIntegerField(
        _("班级排名"),
        null=True,
        blank=True,
        help_text=_("在班级中的排名")
    )
    grade_rank = models.PositiveIntegerField(
        _("年级排名"),
        null=True,
        blank=True,
        help_text=_("在年级中的排名")
    )
    
    # 状态
    is_absent = models.BooleanField(
        _("缺考"),
        default=False,
        help_text=_("学生是否缺考该科目")
    )
    is_cheating = models.BooleanField(
        _("作弊"),
        default=False,
        help_text=_("是否存在作弊行为")
    )
    
    # 备注
    remarks = models.TextField(
        _("备注"),
        blank=True,
        help_text=_("对该成绩的备注说明")
    )
    
    class Meta:
        verbose_name = _("学生成绩")
        verbose_name_plural = _("学生成绩")
        unique_together = ["student", "exam", "subject"]
        ordering = ["-exam__start_date", "subject", "-score"]
        indexes = [
            models.Index(fields=["exam", "subject"]),
            models.Index(fields=["student", "exam"]),
        ]
    
    def __str__(self):
        return f"{self.student.name} - {self.exam.name} - {self.subject.name}: {self.score}"
    
    @property
    def percentage(self):
        """得分率"""
        if self.full_score > 0:
            return round((self.score / self.full_score) * 100, 2)
        return 0
    
    @property
    def is_pass(self):
        """是否及格（60%）"""
        return self.percentage >= 60
