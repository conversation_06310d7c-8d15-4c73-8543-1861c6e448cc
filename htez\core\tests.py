"""
Tests for core models.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import SchoolYear, Semester, Subject


class SchoolYearModelTest(TestCase):
    """Test SchoolYear model"""
    
    def test_create_school_year(self):
        """Test creating a school year"""
        school_year = SchoolYear.objects.create(
            name="2024-2025学年",
            start_year=2024,
            end_year=2025
        )
        self.assertEqual(str(school_year), "2024-2025学年")
        self.assertFalse(school_year.is_current)
    
    def test_only_one_current_school_year(self):
        """Test that only one school year can be current"""
        # Create first school year as current
        school_year1 = SchoolYear.objects.create(
            name="2023-2024学年",
            start_year=2023,
            end_year=2024,
            is_current=True
        )
        self.assertTrue(school_year1.is_current)
        
        # Create second school year as current
        school_year2 = SchoolYear.objects.create(
            name="2024-2025学年",
            start_year=2024,
            end_year=2025,
            is_current=True
        )
        
        # Refresh from database
        school_year1.refresh_from_db()
        
        # First should no longer be current
        self.assertFalse(school_year1.is_current)
        self.assertTrue(school_year2.is_current)


class SemesterModelTest(TestCase):
    """Test Semester model"""
    
    def setUp(self):
        self.school_year = SchoolYear.objects.create(
            name="2024-2025学年",
            start_year=2024,
            end_year=2025
        )
    
    def test_create_semester(self):
        """Test creating a semester"""
        semester = Semester.objects.create(
            school_year=self.school_year,
            semester_number=1,
            start_date="2024-09-01",
            end_date="2025-01-31"
        )
        self.assertEqual(str(semester), "2024-2025学年第1学期")
        self.assertFalse(semester.is_current)
    
    def test_auto_generate_name(self):
        """Test automatic name generation"""
        semester = Semester.objects.create(
            school_year=self.school_year,
            semester_number=2,
            start_date="2025-02-01",
            end_date="2025-07-31"
        )
        self.assertEqual(semester.name, "2024-2025学年第2学期")


class SubjectModelTest(TestCase):
    """Test Subject model"""
    
    def test_create_subject(self):
        """Test creating a subject"""
        subject = Subject.objects.create(
            name="数学",
            code="MATH",
            category="main",
            sort_order=1
        )
        self.assertEqual(str(subject), "数学")
        self.assertTrue(subject.is_active)
    
    def test_unique_constraints(self):
        """Test unique constraints"""
        Subject.objects.create(
            name="数学",
            code="MATH",
            category="main"
        )
        
        # Should not be able to create another subject with same name
        with self.assertRaises(Exception):
            Subject.objects.create(
                name="数学",
                code="MATH2",
                category="main"
            )
        
        # Should not be able to create another subject with same code
        with self.assertRaises(Exception):
            Subject.objects.create(
                name="Mathematics",
                code="MATH",
                category="main"
            )
